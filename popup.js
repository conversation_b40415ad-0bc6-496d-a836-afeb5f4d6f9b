// Popup script for Read Aloud Pal settings panel

class SettingsPanel {
  constructor() {
    this.languageSelect = document.getElementById("language-select");
    this.languageStatus = document.getElementById("language-status");
    this.voiceSelect = document.getElementById("voice-select");
    this.voiceStatus = document.getElementById("voice-status");
    this.repeatSlider = document.getElementById("repeat-slider");
    this.repeatInput = document.getElementById("repeat-input");
    this.repeatDisplay = document.getElementById("repeat-display");
    this.repeatText = document.getElementById("repeat-text");
    this.floatingButtonToggle = document.getElementById(
      "floating-button-toggle"
    );
    this.toggleStatus = document.getElementById("toggle-status");
    this.apiKeyInput = document.getElementById("api-key-input");
    this.apiKeyStatus = document.getElementById("api-key-status");
    this.toggleApiKeyBtn = document.getElementById("toggle-api-key");
    this.historyList = document.getElementById("history-list");
    this.clearHistoryBtn = document.getElementById("clear-history-btn");
    this.isLoading = false;
    this.saveTimeout = null;

    this.init();
  }

  async init() {
    try {
      // Show loading state
      this.setLoadingState(true);

      // Load current settings
      await this.loadSettings();

      // Load history
      await this.loadHistory();

      // Add event listeners
      this.languageSelect.addEventListener(
        "change",
        this.handleLanguageChange.bind(this)
      );
      this.voiceSelect.addEventListener(
        "change",
        this.handleVoiceChange.bind(this)
      );
      this.repeatSlider.addEventListener(
        "input",
        this.handleRepeatChange.bind(this)
      );
      this.repeatInput.addEventListener(
        "input",
        this.handleRepeatInputChange.bind(this)
      );
      this.floatingButtonToggle.addEventListener(
        "change",
        this.handleToggleChange.bind(this)
      );
      this.apiKeyInput.addEventListener(
        "input",
        this.handleApiKeyChange.bind(this)
      );
      this.toggleApiKeyBtn.addEventListener(
        "click",
        this.handleToggleApiKeyVisibility.bind(this)
      );
      this.clearHistoryBtn.addEventListener(
        "click",
        this.handleClearHistory.bind(this)
      );

      // Add keyboard shortcuts
      document.addEventListener("keydown", this.handleKeydown.bind(this));

      console.log("Settings panel initialized");

      // Hide loading state
      this.setLoadingState(false);
    } catch (error) {
      console.error("Error initializing settings panel:", error);
      this.showError("Failed to initialize settings panel");
      this.setLoadingState(false);
    }
  }

  // Set loading state for UI feedback
  setLoadingState(loading) {
    this.isLoading = loading;
    const elements = [
      this.languageSelect,
      this.voiceSelect,
      this.repeatSlider,
      this.repeatInput,
      this.floatingButtonToggle,
      this.apiKeyInput,
      this.toggleApiKeyBtn,
    ];
    elements.forEach((element) => {
      if (element) {
        element.disabled = loading;
      }
    });
  }

  // Handle keyboard shortcuts
  handleKeydown(event) {
    if (event.key === "Escape") {
      window.close();
    }
  }

  async loadSettings() {
    try {
      const settings = await chrome.storage.sync.get([
        "language",
        "voiceName",
        "repeatCount",
        "showFloatingButton",
        "geminiApiKey",
      ]);

      // Set language with validation (default to Traditional Chinese)
      const language = settings.language || "zh-TW";
      if (this.isValidLanguage(language)) {
        this.languageSelect.value = language;
      } else {
        console.warn("Invalid language setting, using default");
        this.languageSelect.value = "zh-TW";
      }
      this.updateLanguageStatus(this.languageSelect.value);

      // Set voice with validation
      const voiceName = settings.voiceName || "Kore";
      this.voiceSelect.value = voiceName;
      this.updateVoiceStatus(voiceName);

      // Set repeat count with validation
      const repeatCount = Math.max(1, Math.min(10, settings.repeatCount || 1));
      this.repeatSlider.value = repeatCount;
      this.repeatInput.value = repeatCount;
      this.updateRepeatDisplay(repeatCount);

      // Set floating button toggle
      const showFloatingButton = settings.showFloatingButton !== false;
      this.floatingButtonToggle.checked = showFloatingButton;
      this.updateToggleStatus(showFloatingButton);

      // Set API key
      const apiKey = settings.geminiApiKey || "";
      this.apiKeyInput.value = apiKey;
      this.updateApiKeyStatus(apiKey);

      console.log("Settings loaded:", {
        language,
        voiceName,
        repeatCount,
        showFloatingButton: settings.showFloatingButton,
        hasApiKey: !!apiKey,
      });
    } catch (error) {
      console.error("Error loading settings:", error);
      this.showError("Failed to load settings");
    }
  }

  // Validate language selection
  isValidLanguage(language) {
    const validLanguages = [
      "zh-TW",
      "zh-CN",
      "en-US",
      "en-GB",
      "ja-JP",
      "ko-KR",
    ];
    return validLanguages.includes(language);
  }

  // Get language display name
  getLanguageDisplayName(language) {
    const languageNames = {
      "zh-TW": "中文 (繁體) - Traditional Chinese",
      "zh-CN": "中文 (簡體) - Simplified Chinese",
      "en-US": "English (US)",
      "en-GB": "English (UK)",
      "ja-JP": "日本語 - Japanese",
      "ko-KR": "한국어 - Korean",
    };
    return languageNames[language] || language;
  }

  // Update language status display
  updateLanguageStatus(language) {
    if (this.languageStatus) {
      this.languageStatus.textContent = `Current: ${this.getLanguageDisplayName(
        language
      )}`;
    }
  }

  // Update voice status display
  updateVoiceStatus(voiceName) {
    if (this.voiceStatus) {
      this.voiceStatus.textContent = `Current: ${this.getVoiceDisplayName(
        voiceName
      )}`;
    }
  }

  // Get voice display name
  getVoiceDisplayName(voiceName) {
    const voiceNames = {
      Kore: "Kore - Firm (堅定)",
      Puck: "Puck - Upbeat (樂觀)",
      Charon: "Charon - Informative (資訊性)",
      Leda: "Leda - Youthful (年輕)",
      Fenrir: "Fenrir - Excitable (興奮)",
      Aoede: "Aoede - Breezy (輕快)",
      Zephyr: "Zephyr - Bright (明亮)",
      Orus: "Orus - Firm (堅定)",
      Callirrhoe: "Callirrhoe - Easy-going (隨和)",
      Autonoe: "Autonoe - Bright (明亮)",
      Enceladus: "Enceladus - Breathy (氣息感)",
      Iapetus: "Iapetus - Clear (清晰)",
      Umbriel: "Umbriel - Easy-going (隨和)",
      Algieba: "Algieba - Smooth (順滑)",
      Despina: "Despina - Smooth (順滑)",
    };
    return voiceNames[voiceName] || voiceName;
  }

  // Show error message to user
  showError(message) {
    // Create or update error element
    let errorElement = document.querySelector(".error-message");
    if (!errorElement) {
      errorElement = document.createElement("div");
      errorElement.className = "error-message";
      document.querySelector(".container").appendChild(errorElement);
    }

    errorElement.textContent = message;
    errorElement.style.display = "block";

    // Hide error after 3 seconds
    setTimeout(() => {
      if (errorElement) {
        errorElement.style.display = "none";
      }
    }, 3000);
  }

  async saveSettings() {
    try {
      const settings = {
        language: this.languageSelect.value,
        voiceName: this.voiceSelect.value,
        repeatCount: parseInt(this.repeatSlider.value),
        showFloatingButton: this.floatingButtonToggle.checked,
        geminiApiKey: this.apiKeyInput.value.trim(),
      };

      await chrome.storage.sync.set(settings);

      // Notify content scripts about settings change
      const tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      if (tabs[0]) {
        chrome.tabs
          .sendMessage(tabs[0].id, {
            action: "settingsChanged",
            settings: settings,
          })
          .catch(() => {
            // Ignore errors if content script is not ready
          });
      }

      console.log("Settings saved:", settings);
    } catch (error) {
      console.error("Error saving settings:", error);
    }
  }

  // Handle language change
  handleLanguageChange() {
    this.updateLanguageStatus(this.languageSelect.value);
    this.saveSettings();
  }

  // Handle voice change
  handleVoiceChange() {
    this.updateVoiceStatus(this.voiceSelect.value);
    this.saveSettings();
  }

  // Handle toggle change
  handleToggleChange() {
    const isEnabled = this.floatingButtonToggle.checked;
    this.updateToggleStatus(isEnabled);
    this.saveSettings();
  }

  // Update toggle status display
  updateToggleStatus(enabled) {
    if (this.toggleStatus) {
      if (enabled) {
        this.toggleStatus.textContent =
          "Floating button will appear when text is selected";
        this.toggleStatus.style.color = "#28a745";
      } else {
        this.toggleStatus.textContent =
          "Only right-click context menu will be available";
        this.toggleStatus.style.color = "#6c757d";
      }
    }
  }

  // Update repeat count display
  updateRepeatDisplay(count) {
    if (this.repeatDisplay) {
      this.repeatDisplay.textContent = count;
    }

    if (this.repeatText) {
      if (count === 1) {
        this.repeatText.textContent = "Text will be read once";
      } else {
        this.repeatText.textContent = `Text will be read ${count} times`;
      }
    }
  }

  handleRepeatChange() {
    // Sync slider with input
    const value = parseInt(this.repeatSlider.value);
    this.repeatInput.value = value;
    this.updateRepeatDisplay(value);
    this.saveSettings();
  }

  handleRepeatInputChange() {
    // Validate and sync input with slider
    let value = parseInt(this.repeatInput.value);

    if (isNaN(value) || value < 1) {
      value = 1;
    } else if (value > 10) {
      value = 10;
    }

    this.repeatInput.value = value;
    this.repeatSlider.value = value;
    this.updateRepeatDisplay(value);
    this.saveSettings();
  }

  // Handle API key input change
  handleApiKeyChange() {
    const apiKey = this.apiKeyInput.value.trim();
    this.updateApiKeyStatus(apiKey);

    // Debounce saving to avoid too frequent saves
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }
    this.saveTimeout = setTimeout(() => {
      this.saveSettings();
    }, 500);
  }

  // Toggle API key visibility
  handleToggleApiKeyVisibility() {
    const isPassword = this.apiKeyInput.type === "password";
    this.apiKeyInput.type = isPassword ? "text" : "password";
    this.toggleApiKeyBtn.textContent = isPassword ? "🙈" : "👁️";
  }

  // Update API key status display
  updateApiKeyStatus(apiKey) {
    if (this.apiKeyStatus) {
      if (apiKey && apiKey.length > 0) {
        if (this.isValidApiKeyFormat(apiKey)) {
          this.apiKeyStatus.textContent = `API key configured (${apiKey.length} chars)`;
          this.apiKeyStatus.className = "configured";
        } else {
          this.apiKeyStatus.textContent = "Invalid API key format";
          this.apiKeyStatus.className = "not-configured";
        }
      } else {
        this.apiKeyStatus.textContent = "No API key configured";
        this.apiKeyStatus.className = "not-configured";
      }
    }
  }

  // Basic API key format validation
  isValidApiKeyFormat(apiKey) {
    // Gemini API keys typically start with "AIza" and are around 39 characters
    return apiKey.startsWith("AIza") && apiKey.length >= 35;
  }

  // Load history from storage
  async loadHistory() {
    try {
      const result = await chrome.storage.local.get(["ttsHistory"]);
      const history = result.ttsHistory || [];
      this.renderHistory(history);
    } catch (error) {
      console.error("Error loading history:", error);
    }
  }

  // Render history list
  renderHistory(history) {
    if (!this.historyList) return;

    if (history.length === 0) {
      this.historyList.innerHTML = `
        <div class="history-empty">
          <span>尚無語音紀錄</span>
          <small>選擇文字並使用TTS功能後，歷史紀錄會顯示在這裡</small>
        </div>
      `;
      return;
    }

    // Sort by timestamp (newest first)
    const sortedHistory = history.sort((a, b) => b.timestamp - a.timestamp);

    this.historyList.innerHTML = sortedHistory
      .map(
        (item) => `
      <div class="history-item" data-id="${item.id}">
        <div class="history-item-header">
          <div class="history-item-text" title="${this.escapeHtml(item.text)}">
            ${this.escapeHtml(item.text)}
          </div>
          <div class="history-item-controls">
            <button class="history-btn play" data-action="play" data-id="${
              item.id
            }">
              ▶️
            </button>
            <button class="history-btn download" data-action="download" data-id="${
              item.id
            }">
              💾
            </button>
          </div>
        </div>
        <div class="history-item-meta">
          <div>
            <span class="history-item-language">${item.language}</span>
            <span class="history-item-voice">${item.voiceName}</span>
          </div>
          <div>${this.formatTimestamp(item.timestamp)}</div>
        </div>
      </div>
    `
      )
      .join("");

    // Add event listeners for history buttons
    this.addHistoryEventListeners();
  }

  // Add event listeners for history buttons
  addHistoryEventListeners() {
    if (!this.historyList) return;

    // Remove existing listeners
    this.historyList.removeEventListener("click", this.handleHistoryClick);

    // Add new listener
    this.handleHistoryClick = (event) => {
      const button = event.target.closest(".history-btn");
      if (!button) return;

      const action = button.getAttribute("data-action");
      const itemId = button.getAttribute("data-id");

      if (action === "play") {
        this.playHistoryItem(itemId);
      } else if (action === "download") {
        this.downloadHistoryItem(itemId);
      }
    };

    this.historyList.addEventListener("click", this.handleHistoryClick);
  }

  // Play history item
  async playHistoryItem(itemId) {
    try {
      const result = await chrome.storage.local.get(["ttsHistory"]);
      const history = result.ttsHistory || [];
      const item = history.find((h) => h.id === itemId);

      if (!item) {
        console.error("History item not found:", itemId);
        return;
      }

      // Create audio context and play
      const audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();
      const audioData = atob(item.audioData);
      const bytes = new Uint8Array(audioData.length);

      for (let i = 0; i < audioData.length; i++) {
        bytes[i] = audioData.charCodeAt(i);
      }

      // Try standard decode first, then PCM
      let audioBuffer;
      try {
        audioBuffer = await audioContext.decodeAudioData(bytes.buffer.slice());
      } catch (e) {
        // Create PCM buffer
        const sampleRate = 24000;
        const channels = 1;
        const bytesPerSample = 2;
        const numSamples = bytes.length / bytesPerSample;

        audioBuffer = audioContext.createBuffer(
          channels,
          numSamples,
          sampleRate
        );
        const channelData = audioBuffer.getChannelData(0);

        for (let i = 0; i < numSamples; i++) {
          const byteIndex = i * bytesPerSample;
          const sample = (bytes[byteIndex + 1] << 8) | bytes[byteIndex];
          const signedSample = sample > 32767 ? sample - 65536 : sample;
          channelData[i] = signedSample / 32768.0;
        }
      }

      // Play audio
      const source = audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContext.destination);
      source.start(0);

      console.log("Playing history item:", item.text.substring(0, 50) + "...");
    } catch (error) {
      console.error("Error playing history item:", error);
      this.showError("播放失敗: " + error.message);
    }
  }

  // Download history item
  async downloadHistoryItem(itemId) {
    try {
      const result = await chrome.storage.local.get(["ttsHistory"]);
      const history = result.ttsHistory || [];
      const item = history.find((h) => h.id === itemId);

      if (!item) {
        console.error("History item not found:", itemId);
        return;
      }

      // Convert base64 to audio data
      const audioData = atob(item.audioData);
      const bytes = new Uint8Array(audioData.length);

      for (let i = 0; i < audioData.length; i++) {
        bytes[i] = audioData.charCodeAt(i);
      }

      // Convert PCM to compressed audio format
      const { blob: audioBlob, extension } =
        await this.convertPCMToCompressedAudio(bytes);
      const url = URL.createObjectURL(audioBlob);

      // Create download link
      const a = document.createElement("a");
      a.href = url;
      a.download = `tts_${item.timestamp}_${item.language}.${extension}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log(
        "Downloaded compressed audio history item:",
        item.text.substring(0, 50) + "..."
      );
    } catch (error) {
      console.error("Error downloading history item:", error);
      this.showError("下載失敗: " + error.message);
    }
  }

  // Convert PCM data to compressed audio using Web Audio API and MediaRecorder
  async convertPCMToCompressedAudio(pcmData) {
    try {
      // Create audio context
      const audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();

      // PCM specifications from Gemini TTS
      const sampleRate = 24000;
      const channels = 1;
      const bytesPerSample = 2;
      const numSamples = pcmData.length / bytesPerSample;

      // Create audio buffer
      const audioBuffer = audioContext.createBuffer(
        channels,
        numSamples,
        sampleRate
      );
      const channelData = audioBuffer.getChannelData(0);

      // Convert 16-bit PCM to float32
      for (let i = 0; i < numSamples; i++) {
        const byteIndex = i * bytesPerSample;
        const sample = (pcmData[byteIndex + 1] << 8) | pcmData[byteIndex];
        const signedSample = sample > 32767 ? sample - 65536 : sample;
        channelData[i] = signedSample / 32768.0;
      }

      // Create offline audio context for rendering
      const offlineContext = new OfflineAudioContext(
        channels,
        numSamples,
        sampleRate
      );
      const source = offlineContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(offlineContext.destination);
      source.start(0);

      // Render audio
      const renderedBuffer = await offlineContext.startRendering();

      // Use MediaRecorder to encode as MP3 (or WebM if MP3 not supported)
      return new Promise((resolve, reject) => {
        const stream = new MediaStream();
        const dest = audioContext.createMediaStreamDestination();

        // Play the rendered audio through MediaStreamDestination
        const playbackSource = audioContext.createBufferSource();
        playbackSource.buffer = renderedBuffer;
        playbackSource.connect(dest);

        stream.addTrack(dest.stream.getAudioTracks()[0]);

        const chunks = [];
        // Try different audio formats in order of preference
        let mimeType = "audio/webm;codecs=opus";
        if (MediaRecorder.isTypeSupported("audio/mp4")) {
          mimeType = "audio/mp4";
        } else if (MediaRecorder.isTypeSupported("audio/webm;codecs=opus")) {
          mimeType = "audio/webm;codecs=opus";
        } else if (MediaRecorder.isTypeSupported("audio/webm")) {
          mimeType = "audio/webm";
        }

        const mediaRecorder = new MediaRecorder(stream, { mimeType });

        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            chunks.push(event.data);
          }
        };

        mediaRecorder.onstop = () => {
          const blob = new Blob(chunks, { type: mimeType });
          const extension = mimeType.includes("mp4")
            ? "mp4"
            : mimeType.includes("webm")
            ? "webm"
            : "audio";
          resolve({ blob, extension });
        };

        mediaRecorder.onerror = (error) => {
          reject(error);
        };

        // Start recording and playback
        mediaRecorder.start();
        playbackSource.start(0);

        // Stop recording after audio duration
        setTimeout(() => {
          mediaRecorder.stop();
        }, renderedBuffer.duration * 1000 + 100); // Add small buffer
      });
    } catch (error) {
      console.error("Error converting to compressed audio:", error);
      // Fallback: return as WAV blob
      return this.createWavBlob(pcmData);
    }
  }

  // Create a valid WAV blob from raw PCM data
  createWavBlob(pcmData) {
    const sampleRate = 24000;
    const numChannels = 1;
    const bitsPerSample = 16;
    const dataSize = pcmData.length;
    const blockAlign = (numChannels * bitsPerSample) / 8;
    const byteRate = sampleRate * blockAlign;

    const buffer = new ArrayBuffer(44 + dataSize);
    const view = new DataView(buffer);

    // RIFF header
    this.writeString(view, 0, "RIFF");
    view.setUint32(4, 36 + dataSize, true);
    this.writeString(view, 8, "WAVE");

    // "fmt " sub-chunk
    this.writeString(view, 12, "fmt ");
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true); // PCM
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitsPerSample, true);

    // "data" sub-chunk
    this.writeString(view, 36, "data");
    view.setUint32(40, dataSize, true);

    // Write PCM data
    for (let i = 0; i < dataSize; i++) {
      view.setUint8(44 + i, pcmData[i]);
    }

    return {
      blob: new Blob([view], { type: "audio/wav" }),
      extension: "wav",
    };
  }

  // Helper to write string to DataView
  writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }

  // Clear all history
  async handleClearHistory() {
    if (confirm("確定要清除所有語音歷史紀錄嗎？")) {
      try {
        await chrome.storage.local.set({ ttsHistory: [] });
        this.renderHistory([]);
        console.log("History cleared");
      } catch (error) {
        console.error("Error clearing history:", error);
        this.showError("清除歷史紀錄失敗");
      }
    }
  }

  // Utility functions
  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return "剛剛";
    if (diffMins < 60) return `${diffMins}分鐘前`;
    if (diffHours < 24) return `${diffHours}小時前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return date.toLocaleDateString("zh-TW", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // Show error message
  showError(message) {
    // Create temporary error display
    const errorDiv = document.createElement("div");
    errorDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #dc3545;
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      z-index: 10000;
      font-size: 14px;
      max-width: 300px;
    `;
    errorDiv.textContent = message;
    document.body.appendChild(errorDiv);

    // Remove after 3 seconds
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.parentNode.removeChild(errorDiv);
      }
    }, 3000);
  }
}

// Initialize settings panel when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.settingsPanel = new SettingsPanel();
});
